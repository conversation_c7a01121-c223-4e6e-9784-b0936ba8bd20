import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from 'vite-plugin-svgr'
import path from "path";
import autoprefixer from 'autoprefixer';
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig(() => ({
  base: "./",
  plugins: [
    react(),
    svgr(),
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  css: {
    postcss: {
      plugins: [
        autoprefixer(), // You can pass options to autoprefixer if needed, e.g., autoprefixer({ overrideBrowserslist: ['last 2 versions'] })
      ],
    },
  },
}));
